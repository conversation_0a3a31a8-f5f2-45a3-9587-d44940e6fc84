-- إدراج الإعدادات الافتراضية في جدول system_settings
-- هذا السكريبت يضيف جميع الإعدادات الأساسية للنظام

USE [databasetasks]
GO

PRINT '🔄 بدء إدراج الإعدادات الافتراضية للنظام...'

-- التحقق من وجود الجدول
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'system_settings')
BEGIN
    PRINT '❌ جدول system_settings غير موجود!'
    RETURN
END

-- حذف البيانات التجريبية الموجودة (إن وجدت)
DELETE FROM system_settings WHERE setting_key LIKE 'test_%' OR setting_key LIKE 'demo_%'
PRINT '🗑️ تم حذف البيانات التجريبية'

-- متغير للوقت الحالي
DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())

-- ===== إعدادات عامة =====
PRINT '📋 إدراج الإعدادات العامة...'

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'app_name')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('app_name', N'نظام إدارة المهام', 'general', N'اسم التطبيق الرسمي', @CurrentTime)
    PRINT '✅ تم إضافة إعداد app_name'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'app_version')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('app_version', '1.0.0', 'general', N'إصدار التطبيق الحالي', @CurrentTime)
    PRINT '✅ تم إضافة إعداد app_version'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'max_users')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('max_users', '1000', 'general', N'الحد الأقصى لعدد المستخدمين', @CurrentTime)
    PRINT '✅ تم إضافة إعداد max_users'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'default_language')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('default_language', 'ar', 'general', N'اللغة الافتراضية للنظام', @CurrentTime)
    PRINT '✅ تم إضافة إعداد default_language'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'timezone')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('timezone', 'Asia/Riyadh', 'general', N'المنطقة الزمنية الافتراضية', @CurrentTime)
    PRINT '✅ تم إضافة إعداد timezone'
END

-- ===== إعدادات الأمان =====
PRINT '🔒 إدراج إعدادات الأمان...'

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'password_min_length')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('password_min_length', '8', 'security', N'الحد الأدنى لطول كلمة المرور', @CurrentTime)
    PRINT '✅ تم إضافة إعداد password_min_length'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'session_timeout')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('session_timeout', '30', 'security', N'مهلة انتهاء الجلسة بالدقائق', @CurrentTime)
    PRINT '✅ تم إضافة إعداد session_timeout'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'max_login_attempts')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('max_login_attempts', '5', 'security', N'الحد الأقصى لمحاولات تسجيل الدخول', @CurrentTime)
    PRINT '✅ تم إضافة إعداد max_login_attempts'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'account_lockout_duration')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('account_lockout_duration', '15', 'security', N'مدة قفل الحساب بالدقائق', @CurrentTime)
    PRINT '✅ تم إضافة إعداد account_lockout_duration'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'require_password_change')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('require_password_change', 'false', 'security', N'إجبار تغيير كلمة المرور دورياً', @CurrentTime)
    PRINT '✅ تم إضافة إعداد require_password_change'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'password_change_days')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('password_change_days', '90', 'security', N'عدد أيام تغيير كلمة المرور', @CurrentTime)
    PRINT '✅ تم إضافة إعداد password_change_days'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'remember_login')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('remember_login', 'true', 'security', N'السماح بتذكر تسجيل الدخول', @CurrentTime)
    PRINT '✅ تم إضافة إعداد remember_login'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'remember_login_days')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('remember_login_days', '7', 'security', N'عدد أيام تذكر تسجيل الدخول', @CurrentTime)
    PRINT '✅ تم إضافة إعداد remember_login_days'
END

-- ===== إعدادات الإشعارات =====
PRINT '🔔 إدراج إعدادات الإشعارات...'

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'enable_email_notifications')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('enable_email_notifications', 'true', 'notifications', N'تفعيل إشعارات البريد الإلكتروني', @CurrentTime)
    PRINT '✅ تم إضافة إعداد enable_email_notifications'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'enable_push_notifications')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('enable_push_notifications', 'true', 'notifications', N'تفعيل الإشعارات الفورية', @CurrentTime)
    PRINT '✅ تم إضافة إعداد enable_push_notifications'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'enable_task_notifications')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('enable_task_notifications', 'true', 'notifications', N'تفعيل إشعارات المهام', @CurrentTime)
    PRINT '✅ تم إضافة إعداد enable_task_notifications'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'enable_message_notifications')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('enable_message_notifications', 'true', 'notifications', N'تفعيل إشعارات الرسائل', @CurrentTime)
    PRINT '✅ تم إضافة إعداد enable_message_notifications'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'enable_system_notifications')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('enable_system_notifications', 'true', 'notifications', N'تفعيل إشعارات النظام', @CurrentTime)
    PRINT '✅ تم إضافة إعداد enable_system_notifications'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'enable_sound_notifications')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('enable_sound_notifications', 'false', 'notifications', N'تفعيل الإشعارات الصوتية', @CurrentTime)
    PRINT '✅ تم إضافة إعداد enable_sound_notifications'
END

-- ===== إعدادات النسخ الاحتياطية =====
PRINT '💾 إدراج إعدادات النسخ الاحتياطية...'

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'auto_backup_enabled')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('auto_backup_enabled', 'true', 'backup', N'تفعيل النسخ الاحتياطية التلقائية', @CurrentTime)
    PRINT '✅ تم إضافة إعداد auto_backup_enabled'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'backup_frequency_hours')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('backup_frequency_hours', '24', 'backup', N'تكرار النسخ الاحتياطية بالساعات', @CurrentTime)
    PRINT '✅ تم إضافة إعداد backup_frequency_hours'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'backup_retention_days')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('backup_retention_days', '30', 'backup', N'مدة الاحتفاظ بالنسخ الاحتياطية بالأيام', @CurrentTime)
    PRINT '✅ تم إضافة إعداد backup_retention_days'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'max_backup_files')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('max_backup_files', '10', 'backup', N'الحد الأقصى لعدد ملفات النسخ الاحتياطية', @CurrentTime)
    PRINT '✅ تم إضافة إعداد max_backup_files'
END

-- ===== إعدادات الملفات =====
PRINT '📁 إدراج إعدادات الملفات...'

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'max_file_size_mb')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('max_file_size_mb', '3072', 'files', N'الحد الأقصى لحجم الملف بالميجابايت (3 جيجابايت)', @CurrentTime)
    PRINT '✅ تم إضافة إعداد max_file_size_mb'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'allowed_file_types')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('allowed_file_types', 'pdf,doc,docx,txt,rtf,odt,jpg,jpeg,png,gif,bmp,webp,mp4,avi,mov,wmv,flv,webm,mp3,wav,flac,aac,ogg,csv,json,xml,xlsx,xls', 'files', N'أنواع الملفات المسموحة', @CurrentTime)
    PRINT '✅ تم إضافة إعداد allowed_file_types'
END

-- ===== إعدادات قاعدة البيانات =====
PRINT '🗄️ إدراج إعدادات قاعدة البيانات...'

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'db_connection_timeout')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('db_connection_timeout', '30', 'database', N'مهلة الاتصال بقاعدة البيانات بالثواني', @CurrentTime)
    PRINT '✅ تم إضافة إعداد db_connection_timeout'
END

IF NOT EXISTS (SELECT 1 FROM system_settings WHERE setting_key = 'db_command_timeout')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_group, description, created_at)
    VALUES ('db_command_timeout', '120', 'database', N'مهلة تنفيذ الأوامر بالثواني', @CurrentTime)
    PRINT '✅ تم إضافة إعداد db_command_timeout'
END

-- عرض النتائج النهائية
PRINT ''
PRINT '📊 ملخص الإعدادات المُدرجة:'
SELECT 
    setting_group AS 'المجموعة',
    COUNT(*) AS 'عدد الإعدادات'
FROM system_settings 
GROUP BY setting_group
ORDER BY setting_group

PRINT ''
PRINT '✅ تم إدراج جميع الإعدادات الافتراضية بنجاح!'

DECLARE @TotalSettings INT = (SELECT COUNT(*) FROM system_settings)
PRINT '🎯 إجمالي الإعدادات: ' + CAST(@TotalSettings AS NVARCHAR(10))

GO
