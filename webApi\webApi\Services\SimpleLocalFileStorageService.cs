using System.Security.Cryptography;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Services;

/// <summary>
/// خدمة التخزين المحلي البسيطة والموثوقة للملفات
/// توفر ميزات النسخ الاحتياطي، التحقق من السلامة، وإدارة الملفات بدون تعقيد
/// </summary>
public class SimpleLocalFileStorageService
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<SimpleLocalFileStorageService> _logger;
    private readonly TasksDbContext _context;

    // إعدادات التخزين
    private readonly string _baseUploadPath;
    private readonly string _backupPath;
    private readonly long _maxFileSize = 3L * 1024 * 1024 * 1024; // 3GB
    private readonly string[] _allowedImageTypes = { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
    private readonly string[] _allowedDocumentTypes = { ".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt" };
    private readonly string[] _allowedDataTypes = { ".csv", ".json", ".xml", ".xlsx", ".xls" };
    private readonly string[] _allowedArchiveTypes = { ".zip", ".rar", ".7z" };

    public SimpleLocalFileStorageService(
        IWebHostEnvironment environment, 
        ILogger<SimpleLocalFileStorageService> logger,
        TasksDbContext context)
    {
        _environment = environment;
        _logger = logger;
        _context = context;
        
        // إعداد مسارات التخزين
        _baseUploadPath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, "uploads");
        _backupPath = Path.Combine(_environment.ContentRootPath, "backups");
        
        // إنشاء المجلدات إذا لم تكن موجودة
        EnsureDirectoriesExist();
    }

    /// <summary>
    /// رفع ملف جديد مع جميع التحسينات
    /// </summary>
    /// <param name="file">الملف المراد رفعه</param>
    /// <param name="taskId">معرف المهمة</param>
    /// <param name="uploadedBy">معرف المستخدم</param>
    /// <param name="folder">مجلد التخزين (اختياري)</param>
    /// <returns>معلومات المرفق المحفوظ</returns>
    public async Task<Attachment?> UploadFileAsync(IFormFile file, int taskId, int uploadedBy, string? folder = null, string? customMetadata = null)
    {
        try
        {
            // التحقق من صحة الملف
            var validationResult = ValidateFile(file);
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("فشل في التحقق من الملف: {Error}", validationResult.ErrorMessage);
                throw new ArgumentException(validationResult.ErrorMessage);
            }

            // تحديد مجلد التخزين
            var storageFolder = DetermineStorageFolder(file, folder);
            var uploadPath = Path.Combine(_baseUploadPath, storageFolder);
            
            // إنشاء المجلد إذا لم يكن موجوداً
            Directory.CreateDirectory(uploadPath);

            // إنشاء اسم ملف فريد
            var uniqueFileName = GenerateUniqueFileName(file.FileName);
            var filePath = Path.Combine(uploadPath, uniqueFileName);

            // حساب هاش الملف الأصلي
            var fileHash = await CalculateFileHashAsync(file);

            // حفظ الملف
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // حفظ معلومات المرفق في قاعدة البيانات
            var attachment = new Attachment
            {
                TaskId = taskId,
                FileName = file.FileName,
                FilePath = $"/uploads/{storageFolder}/{uniqueFileName}",
                FileSize = file.Length,
                FileType = file.ContentType,
                UploadedBy = uploadedBy,
                UploadedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                UniqueFileName = uniqueFileName,
                StorageFolder = storageFolder,
                FileHash = fileHash,
                CompressedSize = null, // لا نضغط الملفات في هذا الإصدار البسيط
                IsCompressed = false,
                Metadata = JsonSerializer.Serialize(new {
                    originalSize = file.Length,
                    processedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    extension = Path.GetExtension(file.FileName).ToLowerInvariant(),
                    type = customMetadata ?? "normal",
                    customData = customMetadata
                }),
                IsDeleted = false,
                DownloadCount = 0,
                IsBackedUp = false
            };

            _context.Attachments.Add(attachment);
            await _context.SaveChangesAsync();

            _logger.LogInformation("تم رفع الملف بنجاح: {FileName} (ID: {Id})", file.FileName, attachment.Id);

            // جدولة النسخ الاحتياطي في الخلفية
            _ = System.Threading.Tasks.Task.Run(() => CreateBackupAsync(attachment));

            return attachment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في رفع الملف: {FileName}", file.FileName);
            throw;
        }
    }

    /// <summary>
    /// تحميل ملف مع تسجيل الوصول
    /// </summary>
    /// <param name="attachmentId">معرف المرفق</param>
    /// <returns>معلومات الملف للتحميل</returns>
    public async Task<(byte[] FileData, string ContentType, string FileName)?> DownloadFileAsync(int attachmentId)
    {
        try
        {
            var attachment = await _context.Attachments
                .FirstOrDefaultAsync(a => a.Id == attachmentId && !a.IsDeleted);

            if (attachment == null)
            {
                _logger.LogWarning("المرفق غير موجود: {AttachmentId}", attachmentId);
                return null;
            }

            var fullPath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, 
                attachment.FilePath.TrimStart('/'));

            if (!File.Exists(fullPath))
            {
                _logger.LogError("الملف غير موجود على القرص: {FilePath}", fullPath);
                return null;
            }

            var fileData = await File.ReadAllBytesAsync(fullPath);

            // تحديث إحصائيات الوصول
            attachment.LastAccessedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            attachment.DownloadCount++;
            await _context.SaveChangesAsync();

            _logger.LogInformation("تم تحميل الملف: {FileName} (مرات التحميل: {Count})", 
                attachment.FileName, attachment.DownloadCount);

            return (fileData, attachment.FileType, attachment.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تحميل الملف: {AttachmentId}", attachmentId);
            throw;
        }
    }

    /// <summary>
    /// حذف ملف (حذف فعلي من القرص)
    /// </summary>
    /// <param name="attachmentId">معرف المرفق</param>
    /// <returns>نتيجة الحذف</returns>
    public async Task<bool> DeleteFileAsync(int attachmentId)
    {
        try
        {
            var attachment = await _context.Attachments.FindAsync(attachmentId);
            if (attachment == null) return false;

            var fullPath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, 
                attachment.FilePath.TrimStart('/'));

            // حذف الملف من القرص
            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
                _logger.LogInformation("تم حذف الملف من القرص: {FilePath}", fullPath);
            }

            // حذف النسخة الاحتياطية
            await DeleteBackupAsync(attachment);

            // تحديث قاعدة البيانات
            attachment.IsDeleted = true;
            await _context.SaveChangesAsync();

            _logger.LogInformation("تم حذف المرفق: {FileName} (ID: {Id})", attachment.FileName, attachmentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في حذف الملف: {AttachmentId}", attachmentId);
            return false;
        }
    }

    /// <summary>
    /// إنشاء نسخة احتياطية للملف
    /// </summary>
    /// <param name="attachment">معلومات المرفق</param>
    private async System.Threading.Tasks.Task CreateBackupAsync(Attachment attachment)
    {
        try
        {
            var sourcePath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, 
                attachment.FilePath.TrimStart('/'));

            if (!File.Exists(sourcePath)) return;

            // إنشاء مجلد النسخ الاحتياطي
            var backupFolder = Path.Combine(_backupPath, attachment.StorageFolder, 
                DateTime.UtcNow.ToString("yyyy-MM"));
            Directory.CreateDirectory(backupFolder);

            var backupFilePath = Path.Combine(backupFolder, attachment.UniqueFileName ?? attachment.FileName);

            // نسخ الملف
            File.Copy(sourcePath, backupFilePath, true);

            // تحديث حالة النسخ الاحتياطي
            attachment.IsBackedUp = true;
            attachment.LastBackupAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            await _context.SaveChangesAsync();

            _logger.LogInformation("تم إنشاء نسخة احتياطية: {FileName}", attachment.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية: {FileName}", attachment.FileName);
        }
    }

    /// <summary>
    /// حذف النسخة الاحتياطية
    /// </summary>
    /// <param name="attachment">معلومات المرفق</param>
    private System.Threading.Tasks.Task DeleteBackupAsync(Attachment attachment)
    {
        try
        {
            var backupFiles = Directory.GetFiles(_backupPath, attachment.UniqueFileName ?? attachment.FileName, 
                SearchOption.AllDirectories);

            foreach (var backupFile in backupFiles)
            {
                File.Delete(backupFile);
                _logger.LogInformation("تم حذف النسخة الاحتياطية: {BackupFile}", backupFile);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في حذف النسخة الاحتياطية: {FileName}", attachment.FileName);
        }

        return System.Threading.Tasks.Task.CompletedTask;
    }

    /// <summary>
    /// التحقق من صحة الملف
    /// </summary>
    /// <param name="file">الملف المراد التحقق منه</param>
    /// <returns>نتيجة التحقق</returns>
    private (bool IsValid, string? ErrorMessage) ValidateFile(IFormFile file)
    {
        if (file == null || file.Length == 0)
            return (false, "لم يتم تحديد ملف للرفع");

        if (file.Length > _maxFileSize)
            return (false, $"حجم الملف كبير جداً. الحد الأقصى {_maxFileSize / (1024L * 1024 * 1024)} GB");

        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        var allowedTypes = _allowedImageTypes.Concat(_allowedDocumentTypes).Concat(_allowedDataTypes).Concat(_allowedArchiveTypes).ToArray();

        if (!allowedTypes.Contains(extension))
            return (false, "نوع الملف غير مدعوم");

        return (true, null);
    }

    /// <summary>
    /// تحديد مجلد التخزين المناسب
    /// </summary>
    /// <param name="file">الملف</param>
    /// <param name="customFolder">مجلد مخصص</param>
    /// <returns>اسم مجلد التخزين</returns>
    private string DetermineStorageFolder(IFormFile file, string? customFolder)
    {
        if (!string.IsNullOrEmpty(customFolder))
            return customFolder;

        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        
        if (_allowedImageTypes.Contains(extension))
            return "images";

        if (_allowedDocumentTypes.Contains(extension))
            return "documents";

        if (_allowedDataTypes.Contains(extension))
            return "data";

        if (_allowedArchiveTypes.Contains(extension))
            return "archives";

        return "attachments";
    }

    /// <summary>
    /// إنشاء اسم ملف فريد
    /// </summary>
    /// <param name="originalFileName">اسم الملف الأصلي</param>
    /// <returns>اسم الملف الفريد</returns>
    private string GenerateUniqueFileName(string originalFileName)
    {
        var extension = Path.GetExtension(originalFileName);
        var uniqueId = Guid.NewGuid().ToString("N");
        return $"{uniqueId}{extension}";
    }

    /// <summary>
    /// حساب هاش الملف للتحقق من السلامة
    /// </summary>
    /// <param name="file">الملف</param>
    /// <returns>هاش الملف</returns>
    private async Task<string> CalculateFileHashAsync(IFormFile file)
    {
        using var sha256 = SHA256.Create();
        using var stream = file.OpenReadStream();
        var hashBytes = await sha256.ComputeHashAsync(stream);
        return Convert.ToHexString(hashBytes);
    }

    /// <summary>
    /// إنشاء المجلدات المطلوبة
    /// </summary>
    private void EnsureDirectoriesExist()
    {
        var directories = new[]
        {
            _baseUploadPath,
            Path.Combine(_baseUploadPath, "attachments"),
            Path.Combine(_baseUploadPath, "images"),
            Path.Combine(_baseUploadPath, "documents"),
            _backupPath
        };

        foreach (var directory in directories)
        {
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.LogInformation("تم إنشاء المجلد: {Directory}", directory);
            }
        }
    }
}