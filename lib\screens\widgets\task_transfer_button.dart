
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/department_controller.dart';
import 'package:flutter_application_2/models/user_model.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import '../../constants/app_colors.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/task_model.dart';
import '../../models/department_model.dart';

import '../../helpers/user_helper.dart';
import 'user_selection_dialog.dart';

/// زر تحويل المهمة
/// يسمح بتحويل المهمة إلى مستخدم آخر مع إضافة تعليق ومرفقات
class TaskTransferButton extends StatefulWidget {
  final Task task;
  final Function(String userId, String comment, List<String> attachments) onTransfer;
  final bool isEnabled;

  const TaskTransferButton({
    super.key,
    required this.task,
    required this.onTransfer,
    this.isEnabled = true,
  });

  @override
  State<TaskTransferButton> createState() => _TaskTransferButtonState();
}

class _TaskTransferButtonState extends State<TaskTransferButton> {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: widget.isEnabled ? () => _showTransferDialog(context) : null,
      icon: const Icon(Icons.send, color: Colors.white),
      label: const Text('تحويل المهمة'),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
       
        elevation:8.0, 
      ),
    );
  }

  /// عرض حوار تحويل المهمة
  /// يستخدم حوار اختيار المستخدم المحسن مع تحميل سريع
  void _showTransferDialog(BuildContext context) async {
    // حفظ مرجع للسياق
    final dialogContext = context;

    // ✅ عرض الحوار فوراً مع مؤشر تحميل
    showDialog(
      context: dialogContext,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري تحميل البيانات...'),
              ],
            ),
          ),
        ),
      ),
    );

    try {
      final userController = Get.find<UserController>();
      final taskController = Get.find<TaskController>();
      final departmentController = Get.find<DepartmentController>();

      // ✅ تحميل البيانات بشكل متوازي (أسرع)
      await Future.wait([
        // تحميل المستخدمين إذا لم يتم تحميلهم
        if (userController.users.isEmpty) userController.loadAllUsers(),

        // ✅ استخدام DepartmentController بدلاً من API مباشرة
        if (departmentController.allDepartments.isEmpty)
          departmentController.loadAllDepartments(),

        // تحميل المساهمين في المهمة
        taskController.loadTaskContributors(widget.task.id),
      ]);

      if (!mounted) return;

      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();

      // الحصول على البيانات من المتحكمات
      final departments = departmentController.allDepartments;

      // الحصول على معرف المستخدم الحالي
      final currentUserId = UserHelper.getCurrentUserId();

      // الحصول على معرفات المساهمين الحاليين في المهمة
      final contributorIds = taskController.taskContributors
          .map((contributor) => contributor.userId)
          .toSet();

      // فلترة المستخدمين لاستبعاد المستخدمين غير المناسبين للتحويل
      final availableUsers = userController.users.where((user) {
        // استبعاد المستخدم الحالي الذي يقوم بالتحويل
        if (user.id == currentUserId) {
          return false;
        }

        // استبعاد المستخدم المكلف حالياً بالمهمة
        if (widget.task.assigneeId != null && user.id == widget.task.assigneeId) {
          return false;
        }

        // استبعاد المساهمين السابقين في المهمة
        if (contributorIds.contains(user.id)) {
          return false;
        }

        // استبعاد المستخدمين غير النشطين
        if (!user.isActive) {
          return false;
        }

        return true;
      }).toList();

      if (!mounted) return;

      // ✅ عرض الحوار النهائي مع البيانات المحملة
      showDialog(
        context: dialogContext,
        builder: (context) => _TransferDialog(
          task: widget.task,
          users: availableUsers,
          departments: departments,
          onTransfer: widget.onTransfer,
        ),
      );
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();
      }

      debugPrint('خطأ في تحميل بيانات تحويل المهمة: $e');

      // عرض رسالة خطأ
      if (mounted) {
        Get.snackbar(
          'خطأ',
          'فشل في تحميل البيانات المطلوبة',
          backgroundColor: AppColors.error,
          colorText: Colors.white,
        );
      }
    }
  }
}

/// حوار تحويل المهمة الداخلي
class _TransferDialog extends StatefulWidget {
  final Task task;
  final List<User> users;
  final List<Department> departments;
  final Function(String userId, String comment, List<String> attachments) onTransfer;

  const _TransferDialog({
    required this.task,
    required this.users,
    required this.departments,
    required this.onTransfer,
  });

  @override
  State<_TransferDialog> createState() => _TransferDialogState();
}

class _TransferDialogState extends State<_TransferDialog> {
  final TextEditingController _commentController = TextEditingController();
  String? _selectedUserId;
  List<PlatformFile> _attachmentFiles = []; // تغيير لحفظ الملفات الفعلية
  bool _isLoading = false;
  bool _isFullscreen = false;

  // قائمة أسباب التحويل الافتراضية
  final List<String> _defaultTransferReasons = [
    'تخصص في المجال المطلوب',
    'نقص في المعلومات لدينا ونحتاج للإفادة',
    'خبرة أكبر في هذا النوع من المهام',
    'متاح حالياً للعمل على هذه المهمة',
    'مسؤول عن هذا القسم',
    'طلب من الإدارة العليا',
    'إعادة تنظيم فريق العمل',
    'سبب آخر (يرجى التوضيح)',
  ];

  String? _selectedReason;
  bool _isCustomReason = false;

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  /// تكبير/تصغير الشاشة
  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Dialog(
      child: Container(
        width: _isFullscreen ? screenSize.width * 0.95 : screenSize.width * 0.6,
        height: _isFullscreen ? screenSize.height * 0.95 : null,
        padding: const EdgeInsets.all(24),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // العنوان
            Row(
              children: [
                const Icon(Icons.send, color: AppColors.primary),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تحويل المهمة',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                // أيقونة تكبير/تصغير الشاشة
                IconButton(
                  onPressed: _toggleFullscreen,
                  icon: Icon(_isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen),
                  tooltip: _isFullscreen ? 'تصغير الشاشة' : 'تكبير الشاشة',
                  color: AppColors.primary,
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: AppColors.primary),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // معلومات المهمة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                physics: const NeverScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المهمة: ${widget.task.title}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    if (widget.task.description?.isNotEmpty == true)
                      Text('الوصف: ${widget.task.description}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // اختيار المستخدم
            Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                   
                     
                      Text(
                        _selectedUserId != null
                            ? 'المستخدم المحدد: ${_getUserName(_selectedUserId!)}'
                            : 'لم يتم اختيار مستخدم',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                ),
                ElevatedButton(

                  onPressed: _showUserSelectionDialog,
                  child: Row(
                    children: [
                      Icon( Icons.transfer_within_a_station_outlined, size: 16,color: Colors.white,),
                      const SizedBox(width: 8),
                      const Text('اختيار مستخدم'),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // اختيار سبب التحويل
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'سبب التحويل:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedReason,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: 'اختر سبب التحويل',
                  ),
                  items: _defaultTransferReasons.map((reason) {
                    return DropdownMenuItem<String>(
                      value: reason,
                      child: Text(reason),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedReason = value;
                      _isCustomReason = value == 'سبب آخر (يرجى التوضيح)';

                      // إذا لم يكن سبب مخصص، ضع السبب في حقل التعليق
                      if (!_isCustomReason && value != null) {
                        _commentController.text = value;
                      } else if (_isCustomReason) {
                        _commentController.text = '';
                      }
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),

            // تعليق التحويل (يظهر دائماً ولكن يتغير النص حسب الاختيار)
            TextField(
              controller: _commentController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: _isCustomReason ? 'تفاصيل السبب' : 'تعليق إضافي (اختياري)',
                hintText: _isCustomReason
                    ? 'اكتب تفاصيل السبب...'
                    : 'أضف أي تعليقات إضافية...',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // المرفقات
            Row(
              children: [
                const Text('المرفقات:', style: TextStyle(fontWeight: FontWeight.bold)),
                const Spacer(),
                TextButton.icon(
                  onPressed: _addAttachment,
                  icon: const Icon(Icons.attach_file),
                  label: const Text('إضافة مرفق'),
                ),
              ],
            ),
            if (_attachmentFiles.isNotEmpty) ...[
              const SizedBox(height: 8),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  itemCount: _attachmentFiles.length,
                  itemBuilder: (context, index) {
                    final file = _attachmentFiles[index];
                    return ListTile(
                      leading: const Icon(Icons.attachment),
                      title: Text(file.name),
                      subtitle: Text('${(file.size / 1024).toStringAsFixed(1)} KB'),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          setState(() {
                            _attachmentFiles.removeAt(index);
                          });
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
            const SizedBox(height: 24),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _selectedUserId != null &&
                  _commentController.text.trim().isNotEmpty &&
                            //  _selectedReason != null &&
                            //  _defaultTransferReasons.contains(_selectedReason) &&
                             (!_isCustomReason || _commentController.text.trim().isNotEmpty) &&
                             !_isLoading
                      ? _performTransfer
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('تحويل'),

                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showUserSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => UserSelectionDialog(
        users: widget.users,
        departments: widget.departments,
        multiSelect: false,
        title: 'اختيار المستخدم لتحويل المهمة',
        onUsersSelected: (userIds) {
          if (userIds.isNotEmpty) {
            setState(() {
              _selectedUserId = userIds.first;
            });
          }
        },
      ),
    );
  }

  Future<void> _addAttachment() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.any,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _attachmentFiles.add(result.files.first);
        });
      }
    } catch (e) {
      debugPrint('خطأ في اختيار الملف: $e');
      Get.snackbar(
        'خطأ',
        'فشل في اختيار الملف: $e',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _performTransfer() async {
    if (_selectedUserId == null || _selectedReason == null) return;

    setState(() => _isLoading = true);

    try {
      // تحضير التعليق النهائي مع السبب
      String finalComment = _selectedReason!;
      if (_commentController.text.trim().isNotEmpty) {
        if (_isCustomReason) {
          finalComment = _commentController.text.trim();
        } else {
          finalComment += '\n\nتعليق إضافي: ${_commentController.text.trim()}';
        }
      }

      // رفع المرفقات أولاً إذا كانت موجودة
      List<String> uploadedAttachmentIds = [];
      if (_attachmentFiles.isNotEmpty) {
        final taskController = Get.find<TaskController>();
        final authController = Get.find<AuthController>();
        final currentUser = authController.currentUser.value;

        if (currentUser != null) {
          for (final platformFile in _attachmentFiles) {
            try {
              // تحويل PlatformFile إلى File
              final file = File(platformFile.path!);

              // رفع المرفق للمهمة مع تحديد أنه مرفق تحويل
              final attachment = await taskController.addAttachment(
                widget.task.id,
                currentUser.id,
                file,
                description: 'مرفق تحويل المهمة: ${platformFile.name}',
                metadata: 'transfer_attachment',
              );

              if (attachment != null) {
                uploadedAttachmentIds.add(attachment.id.toString());
              }
            } catch (e) {
              debugPrint('خطأ في رفع المرفق ${platformFile.name}: $e');
            }
          }
        }
      }

      await widget.onTransfer(
        _selectedUserId!,
        finalComment,
        uploadedAttachmentIds,
      );

      if (mounted) {
        Navigator.of(context).pop();
        Get.snackbar(
          'نجح',
          'تم تحويل المهمة بنجاح',
          backgroundColor: AppColors.success,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تحويل المهمة: $e');
      Get.snackbar(
        'خطأ',
        'فشل في تحويل المهمة',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getUserName(String userId) {
    final user = widget.users.firstWhere(
      (u) => u.id.toString() == userId,
      orElse: () => User(
        id: 0,
        name: 'مستخدم غير معروف',
        email: '',
        role: null,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      ),
    );
    return user.name;
  }
}
