{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["pNyoHL/6itgJMXEC8YVCER+svPduWO9v2n9G3vsZiFI=", "QMF6+VSyJUDtS9yJe9wbp/kqCANZGm4phmbRwLwHTYk=", "9acF85OkR893E++jZhKdlKqb/kWQ5fLX3su56NdkmkQ=", "qdbPppGqDf8BLDW2NExy52tVzwj36YREF/8MgtPEnwg=", "2pn3l3nXG4tYExA03e2y2BU7BVMe9xlHhwldAAqI39w=", "kh0CdOLVz1vV3alzOnSWBIDhuapo1/qf/1xZoxt09xA="], "CachedAssets": {"qdbPppGqDf8BLDW2NExy52tVzwj36YREF/8MgtPEnwg=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\nr15fqvel8-ecem5x2v5n.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76#[.{fingerprint=ecem5x2v5n}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lcfl19kylj", "Integrity": "C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "FileLength": 756, "LastWriteTime": "2025-07-31T21:14:41.1255857+00:00"}, "2pn3l3nXG4tYExA03e2y2BU7BVMe9xlHhwldAAqI39w=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "FileLength": 1927, "LastWriteTime": "2025-07-31T21:14:41.1255857+00:00"}, "kh0CdOLVz1vV3alzOnSWBIDhuapo1/qf/1xZoxt09xA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "FileLength": 1927, "LastWriteTime": "2025-07-31T21:14:41.1255857+00:00"}, "9acF85OkR893E++jZhKdlKqb/kWQ5fLX3su56NdkmkQ=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\f2eqwau0gr-ecem5x2v5n.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa#[.{fingerprint=ecem5x2v5n}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lcfl19kylj", "Integrity": "C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "FileLength": 756, "LastWriteTime": "2025-07-31T21:14:41.1255857+00:00"}, "QMF6+VSyJUDtS9yJe9wbp/kqCANZGm4phmbRwLwHTYk=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ad4wzmw55x-je3vbe9eb6.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd#[.{fingerprint=je3vbe9eb6}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qva2e0zyb4", "Integrity": "at5YydYhql7IaKEFsOXJvgNUvkegiGcimAq420+jGOo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "FileLength": 5246, "LastWriteTime": "2025-07-31T21:14:41.1255857+00:00"}, "pNyoHL/6itgJMXEC8YVCER+svPduWO9v2n9G3vsZiFI=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2)#[.{fingerprint=dz1rjo<PERSON>cz<PERSON>}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00icdbvmee", "Integrity": "II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "FileLength": 7549, "LastWriteTime": "2025-07-31T21:14:41.1255857+00:00"}}, "CachedCopyCandidates": {}}