import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:permission_handler/permission_handler.dart'; // غير متوفر
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart';
import 'package:pdf/widgets.dart' as pw;
import '../models/unified_models.dart';
import 'api/unified_api_services.dart';

/// أنواع التصدير المدعومة
enum ExportFormat { csv, excel, pdf, json }

/// أنواع البيانات المدعومة للتصدير
enum DataType { users, departments, tasks, roles, permissions, reports }

/// أنواع أخطاء التصدير
enum ExportErrorType {
  invalidParameters,
  permissionDenied,
  insufficientStorage,
  noData,
  dataTooLarge,
  fileCreationFailed,
  networkError,
  unknown,
}

/// أنواع أخطاء الاستيراد
enum ImportErrorType {
  fileNotFound,
  invalidFileFormat,
  corruptedFile,
  unsupportedFormat,
  dataValidationFailed,
  dataTooLarge,
  noData,
  permissionDenied,
  networkError,
  unknown,
}

/// استثناء مخصص للتصدير
class ExportException implements Exception {
  final String message;
  final ExportErrorType type;
  final dynamic originalError;

  const ExportException(this.message, this.type, [this.originalError]);

  @override
  String toString() => 'ExportException: $message';

  /// الحصول على رسالة خطأ مفصلة
  String get detailedMessage {
    switch (type) {
      case ExportErrorType.invalidParameters:
        return 'معاملات التصدير غير صالحة. تحقق من نوع البيانات والصيغة المحددة.';
      case ExportErrorType.permissionDenied:
        return 'لا توجد صلاحيات للوصول للتخزين. يرجى منح التطبيق الصلاحيات المطلوبة.';
      case ExportErrorType.insufficientStorage:
        return 'مساحة التخزين غير كافية. يرجى تحرير مساحة إضافية والمحاولة مرة أخرى.';
      case ExportErrorType.noData:
        return 'لا توجد بيانات للتصدير. تأكد من وجود بيانات تطابق المعايير المحددة.';
      case ExportErrorType.dataTooLarge:
        return 'حجم البيانات كبير جداً للتصدير. يرجى تطبيق مرشحات إضافية لتقليل حجم البيانات.';
      case ExportErrorType.fileCreationFailed:
        return 'فشل في إنشاء ملف التصدير. تحقق من صلاحيات الكتابة ومساحة التخزين.';
      case ExportErrorType.networkError:
        return 'خطأ في الشبكة أثناء جلب البيانات. تحقق من اتصال الإنترنت.';
      case ExportErrorType.unknown:
        return 'حدث خطأ غير متوقع أثناء التصدير. يرجى المحاولة مرة أخرى.';
    }
  }
}

/// استثناء مخصص للاستيراد
class ImportException implements Exception {
  final String message;
  final ImportErrorType type;
  final dynamic originalError;

  const ImportException(this.message, this.type, [this.originalError]);

  @override
  String toString() => 'ImportException: $message';

  /// الحصول على رسالة خطأ مفصلة
  String get detailedMessage {
    switch (type) {
      case ImportErrorType.fileNotFound:
        return 'الملف المحدد غير موجود. تأكد من صحة مسار الملف.';
      case ImportErrorType.invalidFileFormat:
        return 'تنسيق الملف غير صالح. تأكد من أن الملف بالصيغة المطلوبة.';
      case ImportErrorType.corruptedFile:
        return 'الملف تالف أو غير قابل للقراءة. جرب ملف آخر.';
      case ImportErrorType.unsupportedFormat:
        return 'صيغة الملف غير مدعومة. الصيغ المدعومة: CSV, Excel, JSON.';
      case ImportErrorType.dataValidationFailed:
        return 'فشل في التحقق من صحة البيانات. تأكد من أن البيانات تطابق التنسيق المطلوب.';
      case ImportErrorType.dataTooLarge:
        return 'حجم الملف كبير جداً للاستيراد. يرجى اختيار ملف أصغر.';
      case ImportErrorType.noData:
        return 'الملف فارغ أو لا يحتوي على بيانات صالحة.';
      case ImportErrorType.permissionDenied:
        return 'لا توجد صلاحيات لقراءة الملف. يرجى منح التطبيق الصلاحيات المطلوبة.';
      case ImportErrorType.networkError:
        return 'خطأ في الشبكة أثناء رفع البيانات. تحقق من اتصال الإنترنت.';
      case ImportErrorType.unknown:
        return 'حدث خطأ غير متوقع أثناء الاستيراد. يرجى المحاولة مرة أخرى.';
    }
  }
}

/// خدمة التصدير والاستيراد الشاملة
/// تدعم تصدير واستيراد البيانات بصيغ مختلفة
class ExportImportService {
  static final ExportImportService _instance = ExportImportService._internal();
  factory ExportImportService() => _instance;
  ExportImportService._internal();

  final UsersApiService _usersApiService = UsersApiService();
  final DepartmentsApiService _departmentsApiService = DepartmentsApiService();
  final TaskApiService _taskApiService = TaskApiService();



  /// تصدير البيانات مع معالجة محسنة للأخطاء
  Future<String?> exportData({
    required DataType dataType,
    required ExportFormat format,
    Map<String, dynamic>? filters,
  }) async {
    try {
      debugPrint('🔄 بدء تصدير ${dataType.name} بصيغة ${format.name}...');

      // التحقق من صحة المعاملات
      if (!_validateExportParameters(dataType, format)) {
        throw ExportException(
          'معاملات التصدير غير صالحة',
          ExportErrorType.invalidParameters,
        );
      }

      // طلب الصلاحيات
      if (!await _requestPermissions()) {
        throw ExportException(
          'لا توجد صلاحيات للوصول للتخزين. يرجى منح التطبيق صلاحية الوصول للملفات.',
          ExportErrorType.permissionDenied,
        );
      }

      // التحقق من مساحة التخزين المتاحة
      if (!await _checkStorageSpace()) {
        throw ExportException(
          'مساحة التخزين غير كافية. يرجى تحرير مساحة إضافية.',
          ExportErrorType.insufficientStorage,
        );
      }

      // الحصول على البيانات مع التحقق من صحتها
      final data = await _fetchDataWithValidation(dataType, filters);
      if (data.isEmpty) {
        throw ExportException(
          'لا توجد بيانات للتصدير. تأكد من وجود بيانات تطابق المعايير المحددة.',
          ExportErrorType.noData,
        );
      }

      // التحقق من حجم البيانات
      if (!_validateDataSize(data)) {
        throw ExportException(
          'حجم البيانات كبير جداً للتصدير. يرجى تطبيق مرشحات إضافية.',
          ExportErrorType.dataTooLarge,
        );
      }

      // تصدير البيانات حسب الصيغة مع معالجة أخطاء محددة
      String? filePath;
      try {
        switch (format) {
          case ExportFormat.csv:
            filePath = await _exportToCsvWithValidation(dataType, data);
            break;
          case ExportFormat.excel:
            filePath = await _exportToExcelWithValidation(dataType, data);
            break;
          case ExportFormat.pdf:
            filePath = await _exportToPdfWithValidation(dataType, data);
            break;
          case ExportFormat.json:
            filePath = await _exportToJsonWithValidation(dataType, data);
            break;
        }
      } catch (e) {
        throw ExportException(
          'فشل في إنشاء ملف التصدير بصيغة ${format.name}: ${e.toString()}',
          ExportErrorType.fileCreationFailed,
        );
      }

      // التحقق من إنشاء الملف بنجاح
      if (!await File(filePath).exists()) {
        throw ExportException(
          'فشل في إنشاء ملف التصدير',
          ExportErrorType.fileCreationFailed,
        );
      }

      debugPrint('✅ تم التصدير بنجاح: $filePath');
      return filePath;
    } on ExportException {
      rethrow; // إعادة رمي الأخطاء المخصصة كما هي
    } catch (e) {
      debugPrint('❌ خطأ غير متوقع في التصدير: $e');
      throw ExportException(
        'حدث خطأ غير متوقع أثناء التصدير: ${e.toString()}',
        ExportErrorType.unknown,
      );
    }
  }

  /// استيراد البيانات من ملف مع معالجة محسنة للأخطاء
  Future<Map<String, dynamic>> importData({
    required DataType dataType,
    required String filePath,
  }) async {
    try {
      debugPrint('🔄 بدء استيراد ${dataType.name} من: $filePath');

      // التحقق من صحة المعاملات
      if (!_validateImportParameters(dataType, filePath)) {
        throw ImportException(
          'معاملات الاستيراد غير صالحة',
          ImportErrorType.invalidFileFormat,
        );
      }

      // التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        throw ImportException(
          'الملف المحدد غير موجود: $filePath',
          ImportErrorType.fileNotFound,
        );
      }

      // التحقق من صلاحيات القراءة
      if (!await _checkFileReadPermissions(file)) {
        throw ImportException(
          'لا توجد صلاحيات لقراءة الملف',
          ImportErrorType.permissionDenied,
        );
      }

      // التحقق من حجم الملف
      if (!await _validateFileSize(file)) {
        throw ImportException(
          'حجم الملف كبير جداً للاستيراد',
          ImportErrorType.dataTooLarge,
        );
      }

      // تحديد نوع الملف والتحقق من دعمه
      final extension = filePath.split('.').last.toLowerCase();
      if (!_isSupportedFileFormat(extension)) {
        throw ImportException(
          'صيغة الملف غير مدعومة: $extension. الصيغ المدعومة: CSV, Excel, JSON',
          ImportErrorType.unsupportedFormat,
        );
      }

      // استيراد البيانات حسب نوع الملف مع معالجة أخطاء محددة
      List<Map<String, dynamic>> importedData = [];
      try {
        switch (extension) {
          case 'csv':
            importedData = await _importFromCsvWithValidation(file);
            break;
          case 'xlsx':
          case 'xls':
            importedData = await _importFromExcelWithValidation(file);
            break;
          case 'json':
            importedData = await _importFromJsonWithValidation(file);
            break;
        }
      } catch (e) {
        throw ImportException(
          'فشل في قراءة الملف بصيغة $extension: ${e.toString()}',
          ImportErrorType.corruptedFile,
          e,
        );
      }

      // التحقق من وجود بيانات
      if (importedData.isEmpty) {
        throw ImportException(
          'الملف فارغ أو لا يحتوي على بيانات صالحة',
          ImportErrorType.noData,
        );
      }

      // التحقق من صحة البيانات المستوردة
      final validationResult = _validateImportedData(importedData, dataType);
      if (!validationResult.isValid) {
        throw ImportException(
          'فشل في التحقق من صحة البيانات: ${validationResult.errors.join(', ')}',
          ImportErrorType.dataValidationFailed,
        );
      }

      // معالجة البيانات المستوردة مع معالجة الأخطاء
      final result = await _processImportedDataWithValidation(dataType, importedData);

      debugPrint('✅ تم الاستيراد بنجاح: ${result['imported']} عنصر');
      return result;
    } on ImportException {
      rethrow; // إعادة رمي الأخطاء المخصصة كما هي
    } catch (e) {
      debugPrint('❌ خطأ غير متوقع في الاستيراد: $e');
      throw ImportException(
        'حدث خطأ غير متوقع أثناء الاستيراد: ${e.toString()}',
        ImportErrorType.unknown,
        e,
      );
    }
  }

  /// اختيار ملف للاستيراد
  Future<String?> pickFileForImport() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'xlsx', 'xls', 'json'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        return result.files.first.path;
      }
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في اختيار الملف: $e');
      return null;
    }
  }

  /// طلب صلاحيات التخزين
  Future<bool> _requestPermissions() async {
    if (kIsWeb) return true;

    // في التطبيق الحقيقي، يمكن استخدام permission_handler
    // final status = await Permission.storage.request();
    // return status.isGranted;

    // مؤقتاً نفترض أن الصلاحيات متاحة
    return true;
  }

  /// الحصول على البيانات حسب النوع
  Future<List<Map<String, dynamic>>> _fetchData(
    DataType dataType,
    Map<String, dynamic>? filters,
  ) async {
    switch (dataType) {
      case DataType.users:
        final users = await _usersApiService.getAllUsers();
        return users.map((u) => u.toJson()).toList();

      case DataType.departments:
        final departments = await _departmentsApiService.getAllDepartments();
        return departments.map((d) => d.toJson()).toList();

      case DataType.tasks:
        final tasks = await _taskApiService.getAllTasks();
        return tasks.map((t) => t.toJson()).toList();

      default:
        return [];
    }
  }

  /// تصدير إلى CSV
  Future<String> _exportToCsv(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    if (data.isEmpty) throw Exception('لا توجد بيانات للتصدير');

    // إنشاء الرؤوس
    final headers = data.first.keys.toList();
    
    // إنشاء الصفوف
    final rows = <List<dynamic>>[headers];
    for (final item in data) {
      rows.add(headers.map((header) => item[header]?.toString() ?? '').toList());
    }

    // تحويل إلى CSV
    final csv = const ListToCsvConverter().convert(rows);

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${dataType.name}_${DateTime.now().millisecondsSinceEpoch}.csv';
    final file = File('${directory.path}/$fileName');
    await file.writeAsString(csv, encoding: utf8);

    return file.path;
  }

  /// تصدير إلى Excel
  Future<String> _exportToExcel(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    final excel = Excel.createExcel();
    final sheet = excel['البيانات'];

    if (data.isNotEmpty) {
      // إضافة الرؤوس
      final headers = data.first.keys.toList();
      for (int i = 0; i < headers.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
            .value = TextCellValue(headers[i]);
      }

      // إضافة البيانات
      for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
        final item = data[rowIndex];
        for (int colIndex = 0; colIndex < headers.length; colIndex++) {
          final value = item[headers[colIndex]]?.toString() ?? '';
          sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: rowIndex + 1,
          )).value = TextCellValue(value);
        }
      }
    }

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${dataType.name}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(excel.encode()!);

    return file.path;
  }

  /// تصدير إلى PDF
  Future<String> _exportToPdf(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    final pdf = pw.Document();

    // إنشاء جدول البيانات
    if (data.isNotEmpty) {
      final headers = data.first.keys.toList();
      
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.TableHelper.fromTextArray(
              headers: headers,
              data: data.map((item) => 
                headers.map((header) => item[header]?.toString() ?? '').toList()
              ).toList(),
              headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              cellAlignment: pw.Alignment.centerRight,
            );
          },
        ),
      );
    }

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${dataType.name}_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(await pdf.save());

    return file.path;
  }

  /// تصدير إلى JSON
  Future<String> _exportToJson(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    final jsonData = {
      'dataType': dataType.name,
      'exportDate': DateTime.now().toIso8601String(),
      'count': data.length,
      'data': data,
    };

    final jsonString = const JsonEncoder.withIndent('  ').convert(jsonData);

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${dataType.name}_${DateTime.now().millisecondsSinceEpoch}.json';
    final file = File('${directory.path}/$fileName');
    await file.writeAsString(jsonString, encoding: utf8);

    return file.path;
  }

  /// استيراد من CSV
  Future<List<Map<String, dynamic>>> _importFromCsv(File file) async {
    final content = await file.readAsString(encoding: utf8);
    final rows = const CsvToListConverter().convert(content);
    
    if (rows.isEmpty) return [];

    final headers = rows.first.map((e) => e.toString()).toList();
    final data = <Map<String, dynamic>>[];

    for (int i = 1; i < rows.length; i++) {
      final row = rows[i];
      final item = <String, dynamic>{};
      for (int j = 0; j < headers.length && j < row.length; j++) {
        item[headers[j]] = row[j];
      }
      data.add(item);
    }

    return data;
  }

  /// استيراد من Excel
  Future<List<Map<String, dynamic>>> _importFromExcel(File file) async {
    final bytes = await file.readAsBytes();
    final excel = Excel.decodeBytes(bytes);
    
    final data = <Map<String, dynamic>>[];
    
    for (final table in excel.tables.keys) {
      final sheet = excel.tables[table]!;
      if (sheet.rows.isEmpty) continue;

      // الحصول على الرؤوس
      final headers = sheet.rows.first
          .map((cell) => cell?.value?.toString() ?? '')
          .toList();

      // معالجة البيانات
      for (int i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final item = <String, dynamic>{};
        
        for (int j = 0; j < headers.length && j < row.length; j++) {
          item[headers[j]] = row[j]?.value?.toString() ?? '';
        }
        data.add(item);
      }
      break; // نأخذ أول جدول فقط
    }

    return data;
  }

  /// استيراد من JSON
  Future<List<Map<String, dynamic>>> _importFromJson(File file) async {
    final content = await file.readAsString(encoding: utf8);
    final jsonData = jsonDecode(content);
    
    if (jsonData is Map && jsonData.containsKey('data')) {
      return List<Map<String, dynamic>>.from(jsonData['data']);
    } else if (jsonData is List) {
      return List<Map<String, dynamic>>.from(jsonData);
    }
    
    return [];
  }

  /// معالجة البيانات المستوردة
  Future<Map<String, dynamic>> _processImportedData(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    int imported = 0;
    int failed = 0;
    final errors = <String>[];

    for (final item in data) {
      try {
        switch (dataType) {
          case DataType.users:
            // معالجة استيراد المستخدمين
            await _importUser(item);
            break;
          case DataType.departments:
            // معالجة استيراد الأقسام
            await _importDepartment(item);
            break;
          case DataType.tasks:
            // معالجة استيراد المهام
            await _importTask(item);
            break;
          default:
            throw Exception('نوع البيانات غير مدعوم للاستيراد');
        }
        imported++;
      } catch (e) {
        failed++;
        errors.add('خطأ في العنصر ${item.toString()}: $e');
      }
    }

    return {
      'imported': imported,
      'failed': failed,
      'errors': errors,
      'total': data.length,
    };
  }

  /// استيراد مستخدم
  Future<void> _importUser(Map<String, dynamic> data) async {
    // تحويل البيانات إلى نموذج User وحفظها
    // يمكن تخصيص هذا حسب بنية البيانات
    await _usersApiService.createUser(User.fromJson(data));
  }

  /// استيراد قسم
  Future<void> _importDepartment(Map<String, dynamic> data) async {
    // تحويل البيانات إلى نموذج Department وحفظها
    await _departmentsApiService.createDepartment(Department.fromJson(data));
  }

  /// استيراد مهمة
  Future<void> _importTask(Map<String, dynamic> data) async {
    // تحويل البيانات إلى نموذج Task وحفظها
    await _taskApiService.createTask(Task.fromJson(data));
  }

  // ===== طرق التحقق والتحسين =====

  /// التحقق من صحة معاملات التصدير
  bool _validateExportParameters(DataType dataType, ExportFormat format) {
    // التحقق من أن نوع البيانات مدعوم
    if (!DataType.values.contains(dataType)) {
      debugPrint('❌ نوع البيانات غير مدعوم: $dataType');
      return false;
    }

    // التحقق من أن صيغة التصدير مدعومة
    if (!ExportFormat.values.contains(format)) {
      debugPrint('❌ صيغة التصدير غير مدعومة: $format');
      return false;
    }

    // التحقق من التوافق بين نوع البيانات والصيغة
    if (dataType == DataType.reports && format == ExportFormat.excel) {
      // التقارير قد تكون كبيرة جداً لـ Excel
      debugPrint('⚠️ تحذير: التقارير قد تكون كبيرة لصيغة Excel');
    }

    return true;
  }

  /// التحقق من مساحة التخزين المتاحة
  Future<bool> _checkStorageSpace() async {
    try {
      final directory = await getApplicationDocumentsDirectory();

      // التحقق من وجود المجلد
      if (!await directory.exists()) {
        debugPrint('❌ مجلد التطبيق غير موجود');
        return false;
      }

      // في التطبيق الحقيقي، يمكن فحص المساحة المتاحة بطرق أخرى
      // هنا نفترض وجود مساحة كافية إذا كان المجلد موجود
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في فحص مساحة التخزين: $e');
      return false;
    }
  }

  /// جلب البيانات مع التحقق من صحتها
  Future<List<Map<String, dynamic>>> _fetchDataWithValidation(
    DataType dataType,
    Map<String, dynamic>? filters,
  ) async {
    try {
      final data = await _fetchData(dataType, filters);

      // التحقق من صحة البيانات
      final validatedData = <Map<String, dynamic>>[];
      for (final item in data) {
        if (_validateDataItem(item, dataType)) {
          validatedData.add(item);
        } else {
          debugPrint('⚠️ تم تجاهل عنصر بيانات غير صالح: $item');
        }
      }

      return validatedData;
    } catch (e) {
      throw ExportException(
        'فشل في جلب البيانات: ${e.toString()}',
        ExportErrorType.networkError,
        e,
      );
    }
  }

  /// التحقق من صحة عنصر البيانات
  bool _validateDataItem(Map<String, dynamic> item, DataType dataType) {
    if (item.isEmpty) return false;

    switch (dataType) {
      case DataType.users:
        return item.containsKey('id') && item.containsKey('name') && item.containsKey('email');
      case DataType.departments:
        return item.containsKey('id') && item.containsKey('name');
      case DataType.tasks:
        return item.containsKey('id') && item.containsKey('title');
      case DataType.roles:
        return item.containsKey('id') && item.containsKey('name');
      case DataType.permissions:
        return item.containsKey('id') && item.containsKey('name');
      case DataType.reports:
        return item.containsKey('id');
    }
  }

  /// التحقق من حجم البيانات
  bool _validateDataSize(List<Map<String, dynamic>> data) {
    const maxItems = 10000; // الحد الأقصى للعناصر
    const maxSizeBytes = 50 * 1024 * 1024; // 50 MB

    if (data.length > maxItems) {
      debugPrint('❌ عدد العناصر كبير جداً: ${data.length} > $maxItems');
      return false;
    }

    // تقدير حجم البيانات
    final estimatedSize = data.length * 1024; // تقدير 1KB لكل عنصر
    if (estimatedSize > maxSizeBytes) {
      debugPrint('❌ حجم البيانات كبير جداً: $estimatedSize > $maxSizeBytes');
      return false;
    }

    return true;
  }

  /// تصدير إلى CSV مع التحقق
  Future<String> _exportToCsvWithValidation(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    try {
      return await _exportToCsv(dataType, data);
    } catch (e) {
      throw ExportException(
        'فشل في تصدير CSV: ${e.toString()}',
        ExportErrorType.fileCreationFailed,
        e,
      );
    }
  }

  /// تصدير إلى Excel مع التحقق
  Future<String> _exportToExcelWithValidation(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    try {
      return await _exportToExcel(dataType, data);
    } catch (e) {
      throw ExportException(
        'فشل في تصدير Excel: ${e.toString()}',
        ExportErrorType.fileCreationFailed,
        e,
      );
    }
  }

  /// تصدير إلى PDF مع التحقق
  Future<String> _exportToPdfWithValidation(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    try {
      return await _exportToPdf(dataType, data);
    } catch (e) {
      throw ExportException(
        'فشل في تصدير PDF: ${e.toString()}',
        ExportErrorType.fileCreationFailed,
        e,
      );
    }
  }

  /// تصدير إلى JSON مع التحقق
  Future<String> _exportToJsonWithValidation(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    try {
      return await _exportToJson(dataType, data);
    } catch (e) {
      throw ExportException(
        'فشل في تصدير JSON: ${e.toString()}',
        ExportErrorType.fileCreationFailed,
        e,
      );
    }
  }

  // ===== طرق التحقق والتحسين للاستيراد =====

  /// التحقق من صحة معاملات الاستيراد
  bool _validateImportParameters(DataType dataType, String filePath) {
    // التحقق من أن نوع البيانات مدعوم
    if (!DataType.values.contains(dataType)) {
      debugPrint('❌ نوع البيانات غير مدعوم: $dataType');
      return false;
    }

    // التحقق من صحة مسار الملف
    if (filePath.isEmpty) {
      debugPrint('❌ مسار الملف فارغ');
      return false;
    }

    return true;
  }

  /// التحقق من صلاحيات قراءة الملف
  Future<bool> _checkFileReadPermissions(File file) async {
    try {
      // محاولة قراءة بايت واحد للتحقق من الصلاحيات
      await file.openRead(0, 1).first;
      return true;
    } catch (e) {
      debugPrint('❌ لا توجد صلاحيات لقراءة الملف: $e');
      return false;
    }
  }

  /// التحقق من حجم الملف
  Future<bool> _validateFileSize(File file) async {
    try {
      final fileSize = await file.length();
      const maxSizeBytes = 3 * 1024 * 1024 * 1024; // 3 GB

      if (fileSize > maxSizeBytes) {
        debugPrint('❌ حجم الملف كبير جداً: $fileSize > $maxSizeBytes');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في فحص حجم الملف: $e');
      return false;
    }
  }

  /// التحقق من دعم صيغة الملف
  bool _isSupportedFileFormat(String extension) {
    const supportedFormats = ['csv', 'xlsx', 'xls', 'json'];
    return supportedFormats.contains(extension.toLowerCase());
  }

  /// استيراد من CSV مع التحقق
  Future<List<Map<String, dynamic>>> _importFromCsvWithValidation(File file) async {
    try {
      return await _importFromCsv(file);
    } catch (e) {
      throw ImportException(
        'فشل في قراءة ملف CSV: ${e.toString()}',
        ImportErrorType.corruptedFile,
        e,
      );
    }
  }

  /// استيراد من Excel مع التحقق
  Future<List<Map<String, dynamic>>> _importFromExcelWithValidation(File file) async {
    try {
      return await _importFromExcel(file);
    } catch (e) {
      throw ImportException(
        'فشل في قراءة ملف Excel: ${e.toString()}',
        ImportErrorType.corruptedFile,
        e,
      );
    }
  }

  /// استيراد من JSON مع التحقق
  Future<List<Map<String, dynamic>>> _importFromJsonWithValidation(File file) async {
    try {
      return await _importFromJson(file);
    } catch (e) {
      throw ImportException(
        'فشل في قراءة ملف JSON: ${e.toString()}',
        ImportErrorType.corruptedFile,
        e,
      );
    }
  }

  /// التحقق من صحة البيانات المستوردة
  ValidationResult _validateImportedData(List<Map<String, dynamic>> data, DataType dataType) {
    final errors = <String>[];

    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final itemErrors = _validateImportedItem(item, dataType, i + 1);
      errors.addAll(itemErrors);
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة عنصر مستورد
  List<String> _validateImportedItem(Map<String, dynamic> item, DataType dataType, int rowNumber) {
    final errors = <String>[];

    switch (dataType) {
      case DataType.users:
        if (!item.containsKey('name') || item['name']?.toString().isEmpty == true) {
          errors.add('الصف $rowNumber: اسم المستخدم مطلوب');
        }
        if (!item.containsKey('email') || item['email']?.toString().isEmpty == true) {
          errors.add('الصف $rowNumber: البريد الإلكتروني مطلوب');
        }
        break;
      case DataType.departments:
        if (!item.containsKey('name') || item['name']?.toString().isEmpty == true) {
          errors.add('الصف $rowNumber: اسم القسم مطلوب');
        }
        break;
      case DataType.tasks:
        if (!item.containsKey('title') || item['title']?.toString().isEmpty == true) {
          errors.add('الصف $rowNumber: عنوان المهمة مطلوب');
        }
        break;
      case DataType.roles:
        if (!item.containsKey('name') || item['name']?.toString().isEmpty == true) {
          errors.add('الصف $rowNumber: اسم الدور مطلوب');
        }
        break;
      case DataType.permissions:
        if (!item.containsKey('name') || item['name']?.toString().isEmpty == true) {
          errors.add('الصف $rowNumber: اسم الصلاحية مطلوب');
        }
        break;
      case DataType.reports:
        if (!item.containsKey('name') || item['name']?.toString().isEmpty == true) {
          errors.add('الصف $rowNumber: اسم التقرير مطلوب');
        }
        break;
    }

    return errors;
  }

  /// معالجة البيانات المستوردة مع التحقق
  Future<Map<String, dynamic>> _processImportedDataWithValidation(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    try {
      return await _processImportedData(dataType, data);
    } catch (e) {
      throw ImportException(
        'فشل في معالجة البيانات المستوردة: ${e.toString()}',
        ImportErrorType.dataValidationFailed,
        e,
      );
    }
  }
}

/// نتيجة التحقق من صحة البيانات
class ValidationResult {
  final bool isValid;
  final List<String> errors;

  ValidationResult({
    required this.isValid,
    required this.errors,
  });
}
