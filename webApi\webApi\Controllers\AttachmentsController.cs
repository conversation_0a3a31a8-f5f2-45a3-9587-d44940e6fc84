using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.SignalR;
using webApi.Models;
using webApi.Services;
using webApi.Hubs;
using Microsoft.AspNetCore.Http.Features;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم محسنة لإدارة المرفقات مع دعم التخزين المحلي المتقدم
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class AttachmentsController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly IWebHostEnvironment _environment;
        private readonly SimpleLocalFileStorageService _fileStorageService;
        private readonly ILogger<AttachmentsController> _logger;
        private readonly IHubContext<TaskHub> _hubContext;
        private readonly INotificationService _notificationService;
        private readonly ILoggingService _loggingService;

        public AttachmentsController(
            TasksDbContext context,
            IWebHostEnvironment environment,
            SimpleLocalFileStorageService fileStorageService,
            ILogger<AttachmentsController> logger,
            IHubContext<TaskHub> hubContext,
            INotificationService notificationService,
            ILoggingService loggingService)
        {
            _context = context;
            _environment = environment;
            _fileStorageService = fileStorageService;
            _logger = logger;
            _hubContext = hubContext;
            _notificationService = notificationService;
            _loggingService = loggingService;
        }

        /// <summary>
        /// الحصول على جميع المرفقات
        /// </summary>
        /// <returns>قائمة بجميع المرفقات</returns>
        /// <response code="200">إرجاع قائمة المرفقات</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Attachment>>> GetAttachments()
        {
            try
            {
                return await _context.Attachments
                    .Include(a => a.Task)
                    .Include(a => a.UploadedByNavigation)
                    .Where(a => !a.IsDeleted)
                    .OrderByDescending(a => a.UploadedAt)
                    .ToListAsync();
            }
            catch (Microsoft.Data.SqlClient.SqlException ex) when (
                ex.Message.Contains("Login failed") ||
                ex.Message.Contains("session is in the kill state") ||
                ex.Message.Contains("severe error occurred"))
            {
                _logger.LogWarning("اكتشاف مشكلة في الاتصال بقاعدة البيانات أثناء جلب جميع المرفقات: {Error}", ex.Message);

                try
                {
                    // تنظيف connection pools
                    Microsoft.Data.SqlClient.SqlConnection.ClearAllPools();

                    // انتظار قصير
                    await System.Threading.Tasks.Task.Delay(1000);

                    // إعادة المحاولة مع context جديد
                    using var newContext = new TasksDbContext();
                    var result = await newContext.Attachments
                        .Include(a => a.Task)
                        .Include(a => a.UploadedByNavigation)
                        .Where(a => !a.IsDeleted)
                        .OrderByDescending(a => a.UploadedAt)
                        .ToListAsync();

                    return result;
                }
                catch (Exception retryEx)
                {
                    _logger.LogError(retryEx, "فشل في إعادة المحاولة لجلب جميع المرفقات");

                    if (retryEx.Message.Contains("Login failed"))
                    {
                        return StatusCode(503, new {
                            message = "مشكلة في صلاحيات قاعدة البيانات، يرجى الاتصال بالمسؤول",
                            error = "DATABASE_LOGIN_FAILED",
                            details = "تم تغيير إعدادات قاعدة البيانات، يرجى إعادة تكوين الصلاحيات"
                        });
                    }

                    return StatusCode(503, new {
                        message = "قاعدة البيانات غير متاحة مؤقتاً، يرجى المحاولة بعد قليل",
                        error = "DATABASE_UNAVAILABLE"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ عام في جلب جميع المرفقات");
                return StatusCode(500, new { message = "حدث خطأ أثناء جلب المرفقات" });
            }
        }

        /// <summary>
        /// الحصول على مرفق محدد
        /// </summary>
        /// <param name="id">معرف المرفق</param>
        /// <returns>المرفق المطلوب</returns>
        /// <response code="200">إرجاع المرفق</response>
        /// <response code="404">المرفق غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Attachment>> GetAttachment(int id)
        {
            var attachment = await _context.Attachments
                .Include(a => a.Task)
                .Include(a => a.UploadedByNavigation)
                .FirstOrDefaultAsync(a => a.Id == id && !a.IsDeleted);

            if (attachment == null)
            {
                return NotFound();
            }

            return attachment;
        }

        /// <summary>
        /// الحصول على مرفقات مهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>قائمة مرفقات المهمة</returns>
        /// <response code="200">إرجاع قائمة المرفقات</response>
        [HttpGet("task/{taskId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Attachment>>> GetTaskAttachments(int taskId)
        {
            try
            {
                return await _context.Attachments
                    .Include(a => a.UploadedByNavigation)
                    .Where(a => a.TaskId == taskId && !a.IsDeleted)
                    .OrderByDescending(a => a.UploadedAt)
                    .ToListAsync();
            }
            catch (Microsoft.Data.SqlClient.SqlException ex) when (
                ex.Message.Contains("Login failed") ||
                ex.Message.Contains("session is in the kill state") ||
                ex.Message.Contains("severe error occurred"))
            {
                _logger.LogWarning("اكتشاف مشكلة في الاتصال بقاعدة البيانات أثناء جلب مرفقات المهمة {TaskId}: {Error}", taskId, ex.Message);

                try
                {
                    // تنظيف connection pools
                    Microsoft.Data.SqlClient.SqlConnection.ClearAllPools();

                    // انتظار قصير
                    await System.Threading.Tasks.Task.Delay(1000);

                    // إعادة المحاولة مع context جديد
                    using var newContext = new TasksDbContext();
                    var result = await newContext.Attachments
                        .Include(a => a.UploadedByNavigation)
                        .Where(a => a.TaskId == taskId && !a.IsDeleted)
                        .OrderByDescending(a => a.UploadedAt)
                        .ToListAsync();

                    return result;
                }
                catch (Exception retryEx)
                {
                    _logger.LogError(retryEx, "فشل في إعادة المحاولة لجلب مرفقات المهمة {TaskId}", taskId);

                    if (retryEx.Message.Contains("Login failed"))
                    {
                        return StatusCode(503, new {
                            message = "مشكلة في صلاحيات قاعدة البيانات، يرجى الاتصال بالمسؤول",
                            error = "DATABASE_LOGIN_FAILED",
                            details = "تم تغيير إعدادات قاعدة البيانات، يرجى إعادة تكوين الصلاحيات"
                        });
                    }

                    return StatusCode(503, new {
                        message = "قاعدة البيانات غير متاحة مؤقتاً، يرجى المحاولة بعد قليل",
                        error = "DATABASE_UNAVAILABLE"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ عام في جلب مرفقات المهمة {TaskId}", taskId);
                return StatusCode(500, new { message = "حدث خطأ أثناء جلب المرفقات" });
            }
        }

        /// <summary>
        /// الحصول على مرفقات مستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة مرفقات المستخدم</returns>
        /// <response code="200">إرجاع قائمة المرفقات</response>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Attachment>>> GetUserAttachments(int userId)
        {
            return await _context.Attachments
                .Include(a => a.Task)
                .Where(a => a.UploadedBy == userId && !a.IsDeleted)
                .OrderByDescending(a => a.UploadedAt)
                .ToListAsync();
        }

        /// <summary>
        /// عدد مرفقات مهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>عدد المرفقات</returns>
        /// <response code="200">إرجاع العدد</response>
        [HttpGet("task/{taskId}/count")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<int>> GetTaskAttachmentsCount(int taskId)
        {
            var count = await _context.Attachments
                .CountAsync(a => a.TaskId == taskId && !a.IsDeleted);

            return count;
        }

        /// <summary>
        /// الحصول على مرفقات عدة مهام مرة واحدة (محسن للأداء)
        /// </summary>
        /// <param name="taskIds">قائمة معرفات المهام</param>
        /// <returns>مرفقات المهام مجمعة حسب معرف المهمة</returns>
        /// <response code="200">إرجاع المرفقات مجمعة</response>
        [HttpPost("tasks/batch")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<Dictionary<int, List<Attachment>>>> GetTasksAttachmentsBatch([FromBody] List<int> taskIds)
        {
            try
            {
                if (taskIds == null || !taskIds.Any())
                {
                    return Ok(new Dictionary<int, List<Attachment>>());
                }

                // تحديد عدد المهام لتجنب الحمل الزائد
                var limitedTaskIds = taskIds.Take(50).ToList();

                var attachments = await _context.Attachments
                    .Include(a => a.UploadedByNavigation)
                    .Where(a => limitedTaskIds.Contains(a.TaskId) && !a.IsDeleted)
                    .OrderByDescending(a => a.UploadedAt)
                    .ToListAsync();

                // تجميع المرفقات حسب معرف المهمة
                var groupedAttachments = attachments
                    .GroupBy(a => a.TaskId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // إضافة قوائم فارغة للمهام التي لا تحتوي على مرفقات
                foreach (var taskId in limitedTaskIds)
                {
                    if (!groupedAttachments.ContainsKey(taskId))
                    {
                        groupedAttachments[taskId] = new List<Attachment>();
                    }
                }

                return Ok(groupedAttachments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مرفقات المهام المتعددة");
                return StatusCode(500, new { message = "خطأ في جلب المرفقات", error = ex.Message });
            }
        }

        /// <summary>
        /// رفع مرفق جديد مع التحسينات المتقدمة (ضغط، نسخ احتياطي، تحقق من السلامة)
        /// </summary>
        /// <param name="file">الملف المرفق</param>
        /// <param name="taskId">معرف المهمة</param>
        /// <param name="uploadedBy">معرف المستخدم الذي رفع الملف</param>
        /// <param name="folder">مجلد التخزين المخصص (اختياري)</param>
        /// <returns>المرفق المُنشأ مع جميع المعلومات المحسنة</returns>
        /// <response code="201">تم رفع المرفق بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="413">حجم الملف كبير جداً</response>
        /// <response code="415">نوع الملف غير مدعوم</response>
        [HttpPost("upload")]
        [RequestSizeLimit(3L * 1024 * 1024 * 1024)] // 3GB
        [RequestFormLimits(MultipartBodyLengthLimit = 3L * 1024 * 1024 * 1024)] // 3GB
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status413PayloadTooLarge)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<Attachment>> UploadAttachment(
            IFormFile file,
            [FromForm] int taskId,
            [FromForm] int uploadedBy,
            [FromForm] string? folder = null,
            [FromForm] string? metadata = null)
        {
            try
            {
                _logger.LogInformation("بدء رفع مرفق جديد: {FileName} للمهمة {TaskId}", file?.FileName, taskId);

                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { message = "لم يتم تحديد ملف للرفع" });
                }

                // التحقق من وجود المهمة
                var taskExists = await _context.Tasks.AnyAsync(t => t.Id == taskId && !t.IsDeleted);
                if (!taskExists)
                {
                    return BadRequest(new { message = "المهمة غير موجودة" });
                }

                // التحقق من وجود المستخدم
                var userExists = await _context.Users.AnyAsync(u => u.Id == uploadedBy);
                if (!userExists)
                {
                    return BadRequest(new { message = "المستخدم الذي رفع المرفق غير موجود" });
                }

                // استخدام الخدمة المحسنة لرفع الملف
                var attachment = await _fileStorageService.UploadFileAsync(file, taskId, uploadedBy, folder, metadata);
                
                if (attachment == null)
                {
                    return StatusCode(500, new { message = "فشل في رفع الملف" });
                }

                _logger.LogInformation("تم رفع المرفق بنجاح: {FileName} (ID: {Id})", attachment.FileName, attachment.Id);

                // إضافة سجل TaskHistory لرفع المرفق
                var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var taskHistory = new TaskHistory
                {
                    TaskId = taskId,
                    UserId = uploadedBy,
                    Action = "إضافة_مرفق",
                    Details = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        attachmentId = attachment.Id,
                        fileName = attachment.FileName,
                        fileSize = attachment.FileSize,
                        fileType = attachment.FileType
                    }),
                    Timestamp = now,
                    ChangeType = "مرفق",
                    ChangeDescription = $"تمت إضافة مرفق: {attachment.FileName}",
                    OldValue = "",
                    NewValue = attachment.FileName,
                    ChangedBy = uploadedBy,
                    ChangedAt = now
                };

                _context.TaskHistories.Add(taskHistory);
                await _context.SaveChangesAsync();

                // إرسال إشعارات للمستخدمين المعنيين
                await CreateAttachmentNotifications(attachment, "attachment_added");

                // إرسال إشعار SignalR للمستخدمين المهتمين بالمهمة
                try
                {
                    // التأكد من أن القيم ليست null قبل الإرسال
                    var attachmentId = attachment.Id;
                    var fileName = attachment.FileName ?? "ملف غير معروف";

                    await _hubContext.Clients.Group($"Task_{taskId}")
                        .SendAsync("AttachmentAdded", taskId, attachmentId, fileName);

                    _logger.LogInformation("تم إرسال إشعار SignalR لإضافة المرفق {AttachmentId} للمهمة {TaskId}",
                        attachmentId, taskId);
                }
                catch (Exception signalREx)
                {
                    // لا نريد أن يفشل رفع المرفق بسبب SignalR
                    _logger.LogWarning(signalREx, "خطأ في إرسال إشعار SignalR للمرفق {AttachmentId}", attachment.Id);
                }

                // إرجاع المرفق مع جميع المعلومات المحسنة
                return CreatedAtAction("GetAttachment", new { id = attachment.Id }, new
                {
                    attachment.Id,
                    attachment.TaskId,
                    attachment.FileName,
                    attachment.FilePath,
                    attachment.FileSize,
                    attachment.FileType,
                    attachment.UploadedBy,
                    attachment.UploadedAt,
                    attachment.UniqueFileName,
                    attachment.StorageFolder,
                    attachment.IsCompressed,
                    attachment.CompressedSize,
                    FileSizeFormatted = attachment.FileSizeFormatted,
                    CompressionRatio = attachment.CompressionRatio,
                    IsImage = attachment.IsImage,
                    IsDocument = attachment.IsDocument,
                    IsVideo = attachment.IsVideo,
                    IsAudio = attachment.IsAudio
                });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("خطأ في التحقق من الملف: {Error}", ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في رفع المرفق: {FileName}", file?.FileName);
                return StatusCode(500, new { message = "حدث خطأ أثناء رفع الملف" });
            }
        }

        /// <summary>
        /// إنشاء مرفق جديد (بدون رفع ملف)
        /// </summary>
        /// <param name="attachment">بيانات المرفق</param>
        /// <returns>المرفق المُنشأ</returns>
        /// <response code="201">تم إنشاء المرفق بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<Attachment>> PostAttachment(Attachment attachment)
        {
            attachment.UploadedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            attachment.IsDeleted = false;

            _context.Attachments.Add(attachment);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetAttachment", new { id = attachment.Id }, attachment);
        }

        /// <summary>
        /// تحديث مرفق
        /// </summary>
        /// <param name="id">معرف المرفق</param>
        /// <param name="attachment">بيانات المرفق المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث المرفق بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">المرفق غير موجود</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutAttachment(int id, Attachment attachment)
        {
            if (id != attachment.Id)
            {
                return BadRequest();
            }

            _context.Entry(attachment).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AttachmentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// تحميل ملف مرفق مع تسجيل الإحصائيات
        /// </summary>
        /// <param name="id">معرف المرفق</param>
        /// <returns>الملف للتحميل مع تحديث إحصائيات الوصول</returns>
        /// <response code="200">إرجاع الملف</response>
        /// <response code="404">المرفق غير موجود</response>
        [HttpGet("{id}/download")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DownloadAttachment(int id)
        {
            try
            {
                _logger.LogInformation("طلب تحميل المرفق: {AttachmentId}", id);

                var fileData = await _fileStorageService.DownloadFileAsync(id);
                if (fileData == null)
                {
                    return NotFound(new { message = "المرفق غير موجود" });
                }

                _logger.LogInformation("تم تحميل المرفق بنجاح: {FileName}", fileData.Value.FileName);

                return File(fileData.Value.FileData, fileData.Value.ContentType, fileData.Value.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل المرفق: {AttachmentId}", id);
                return StatusCode(500, new { message = "حدث خطأ أثناء تحميل الملف" });
            }
        }

        /// <summary>
        /// حذف مرفق (حذف فعلي من القرص مع النسخ الاحتياطية)
        /// </summary>
        /// <param name="id">معرف المرفق</param>
        /// <param name="permanent">حذف نهائي من القرص (افتراضي: false)</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف المرفق بنجاح</response>
        /// <response code="404">المرفق غير موجود</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteAttachment(int id, [FromQuery] bool permanent = false)
        {
            try
            {
                _logger.LogInformation("طلب حذف المرفق: {AttachmentId} (نهائي: {Permanent})", id, permanent);

                var attachment = await _context.Attachments.FindAsync(id);
                if (attachment == null || attachment.IsDeleted)
                {
                    return NotFound(new { message = "المرفق غير موجود" });
                }

                // حفظ معرف المهمة لإرسال إشعار SignalR
                var taskId = attachment.TaskId;

                // إضافة سجل TaskHistory لحذف المرفق
                var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var currentUserId = GetCurrentUserId();
                var taskHistory = new TaskHistory
                {
                    TaskId = taskId,
                    UserId = currentUserId > 0 ? currentUserId : attachment.UploadedBy,
                    Action = permanent ? "حذف_مرفق_نهائي" : "حذف_مرفق",
                    Details = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        attachmentId = attachment.Id,
                        fileName = attachment.FileName,
                        fileSize = attachment.FileSize,
                        fileType = attachment.FileType,
                        permanent = permanent
                    }),
                    Timestamp = now,
                    ChangeType = "مرفق",
                    ChangeDescription = permanent ?
                        $"تم حذف المرفق نهائياً: {attachment.FileName}" :
                        $"تم حذف المرفق: {attachment.FileName}",
                    OldValue = attachment.FileName,
                    NewValue = "",
                    ChangedBy = currentUserId > 0 ? currentUserId : attachment.UploadedBy,
                    ChangedAt = now
                };

                _context.TaskHistories.Add(taskHistory);

                if (permanent)
                {
                    // حذف نهائي من القرص
                    var deleted = await _fileStorageService.DeleteFileAsync(id);
                    if (!deleted)
                    {
                        return StatusCode(500, new { message = "فشل في حذف الملف" });
                    }
                }
                else
                {
                    // حذف منطقي فقط
                    attachment.IsDeleted = true;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حذف المرفق بنجاح: {AttachmentId}", id);

                // إرسال إشعارات للمستخدمين المعنيين
                await CreateAttachmentNotifications(attachment, "attachment_deleted");

                // إرسال إشعار SignalR للمستخدمين المهتمين بالمهمة
                try
                {
                    // التأكد من أن القيم ليست null قبل الإرسال
                    var fileName = attachment.FileName ?? "ملف غير معروف";

                    await _hubContext.Clients.Group($"Task_{taskId}")
                        .SendAsync("AttachmentDeleted", taskId, id, fileName);

                    _logger.LogInformation("تم إرسال إشعار SignalR لحذف المرفق {AttachmentId} من المهمة {TaskId}",
                        id, taskId);
                }
                catch (Exception signalREx)
                {
                    // لا نريد أن يفشل حذف المرفق بسبب SignalR
                    _logger.LogWarning(signalREx, "خطأ في إرسال إشعار SignalR لحذف المرفق {AttachmentId}", id);
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المرفق: {AttachmentId}", id);
                return StatusCode(500, new { message = "حدث خطأ أثناء حذف الملف" });
            }
        }

        /// <summary>
        /// الحصول على إحصائيات المرفقات
        /// </summary>
        /// <returns>إحصائيات شاملة عن المرفقات</returns>
        /// <response code="200">إرجاع الإحصائيات</response>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetAttachmentsStatistics()
        {
            try
            {
                var stats = await _context.Attachments
                    .Where(a => !a.IsDeleted)
                    .GroupBy(a => 1)
                    .Select(g => new
                    {
                        TotalFiles = g.Count(),
                        TotalSize = g.Sum(a => a.FileSize),
                        CompressedFiles = g.Count(a => a.IsCompressed),
                        TotalCompressedSize = g.Sum(a => a.CompressedSize ?? 0),
                        BackedUpFiles = g.Count(a => a.IsBackedUp),
                        TotalDownloads = g.Sum(a => a.DownloadCount),
                        FileTypes = g.GroupBy(a => a.FileType)
                                   .Select(ft => new { Type = ft.Key, Count = ft.Count() })
                                   .ToList(),
                        StorageFolders = g.GroupBy(a => a.StorageFolder)
                                        .Select(sf => new { Folder = sf.Key, Count = sf.Count() })
                                        .ToList()
                    })
                    .FirstOrDefaultAsync();

                if (stats == null)
                {
                    return Ok(new
                    {
                        TotalFiles = 0,
                        TotalSize = 0L,
                        CompressedFiles = 0,
                        TotalCompressedSize = 0L,
                        BackedUpFiles = 0,
                        TotalDownloads = 0,
                        FileTypes = new object[0],
                        StorageFolders = new object[0]
                    });
                }

                // حساب نسبة الضغط الإجمالية
                var compressionRatio = stats.TotalSize > 0 
                    ? (double)stats.TotalCompressedSize / stats.TotalSize 
                    : 0;

                return Ok(new
                {
                    stats.TotalFiles,
                    stats.TotalSize,
                    TotalSizeFormatted = FormatFileSize(stats.TotalSize),
                    stats.CompressedFiles,
                    stats.TotalCompressedSize,
                    TotalCompressedSizeFormatted = FormatFileSize(stats.TotalCompressedSize),
                    CompressionRatio = compressionRatio,
                    SpaceSaved = stats.TotalSize - stats.TotalCompressedSize,
                    SpaceSavedFormatted = FormatFileSize(stats.TotalSize - stats.TotalCompressedSize),
                    stats.BackedUpFiles,
                    BackupPercentage = stats.TotalFiles > 0 ? (double)stats.BackedUpFiles / stats.TotalFiles * 100 : 0,
                    stats.TotalDownloads,
                    stats.FileTypes,
                    stats.StorageFolders
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إحصائيات المرفقات");
                return StatusCode(500, new { message = "حدث خطأ أثناء جلب الإحصائيات" });
            }
        }

        /// <summary>
        /// تنظيف الملفات المحذوفة منطقياً (حذف نهائي)
        /// </summary>
        /// <param name="olderThanDays">حذف الملفات المحذوفة منذ أكثر من عدد الأيام المحدد</param>
        /// <returns>عدد الملفات المحذوفة</returns>
        /// <response code="200">تم التنظيف بنجاح</response>
        [HttpPost("cleanup")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> CleanupDeletedFiles([FromQuery] int olderThanDays = 30)
        {
            try
            {
                var cutoffDate = DateTimeOffset.UtcNow.AddDays(-olderThanDays).ToUnixTimeSeconds();
                
                var deletedAttachments = await _context.Attachments
                    .Where(a => a.IsDeleted && a.UploadedAt < cutoffDate)
                    .ToListAsync();

                var cleanedCount = 0;
                foreach (var attachment in deletedAttachments)
                {
                    var deleted = await _fileStorageService.DeleteFileAsync(attachment.Id);
                    if (deleted) cleanedCount++;
                }

                _logger.LogInformation("تم تنظيف {Count} ملف من أصل {Total}", cleanedCount, deletedAttachments.Count);

                return Ok(new
                {
                    TotalFound = deletedAttachments.Count,
                    Cleaned = cleanedCount,
                    Failed = deletedAttachments.Count - cleanedCount,
                    Message = $"تم تنظيف {cleanedCount} ملف بنجاح"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنظيف الملفات المحذوفة");
                return StatusCode(500, new { message = "حدث خطأ أثناء التنظيف" });
            }
        }

        /// <summary>
        /// تنسيق حجم الملف
        /// </summary>
        /// <param name="bytes">الحجم بالبايت</param>
        /// <returns>الحجم المنسق</returns>
        private static string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024):F1} MB";
            return $"{bytes / (1024.0 * 1024 * 1024):F1} GB";
        }

        private bool AttachmentExists(int id)
        {
            return _context.Attachments.Any(e => e.Id == id && !e.IsDeleted);
        }



        /// <summary>
        /// إنشاء إشعارات للمستخدمين المعنيين بالمرفق
        /// </summary>
        private async System.Threading.Tasks.Task CreateAttachmentNotifications(Attachment attachment, string notificationType)
        {
            try
            {
                // الحصول على بيانات المهمة
                var task = await _context.Tasks
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .FirstOrDefaultAsync(t => t.Id == attachment.TaskId);

                if (task == null) return;

                // الحصول على المستخدمين المعنيين بالمهمة
                var userIds = new List<int>();

                // إضافة منشئ المهمة
                if (task.CreatorId != attachment.UploadedBy)
                {
                    userIds.Add(task.CreatorId);
                }

                // إضافة المسند له
                if (task.AssigneeId.HasValue &&
                    task.AssigneeId.Value != attachment.UploadedBy &&
                    !userIds.Contains(task.AssigneeId.Value))
                {
                    userIds.Add(task.AssigneeId.Value);
                }

                // إضافة المستخدمين الذين لهم وصول للمهمة
                var accessUsers = await _context.TaskAccessUsers
                    .Where(au => au.TaskId == attachment.TaskId &&
                               au.UserId != attachment.UploadedBy &&
                               !userIds.Contains(au.UserId))
                    .Select(au => au.UserId)
                    .ToListAsync();

                userIds.AddRange(accessUsers);

                // إرسال الإشعارات
                if (userIds.Count > 0)
                {
                    var uploaderUser = await _context.Users.FindAsync(attachment.UploadedBy);
                    var uploaderName = uploaderUser?.Name ?? "مستخدم";

                    string title = notificationType == "attachment_added" ? "مرفق جديد" : "تم حذف مرفق";
                    string action = notificationType == "attachment_added" ? "أضاف" : "حذف";

                    await _notificationService.CreateAndSendNotificationsAsync(
                        userIds,
                        title,
                        $"المهمة رقم #{task.Id}: {action} {uploaderName} مرفق ({attachment.FileName}) في المهمة '{task.Title}'",
                        notificationType,
                        task.Id
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء إشعارات المرفق: {AttachmentId}", attachment.Id);
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي من JWT token
        /// </summary>
        private int GetCurrentUserId()
        {
            try
            {
                var userIdClaim = User.FindFirst("UserId")?.Value ??
                                 User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

                if (int.TryParse(userIdClaim, out int userId))
                {
                    return userId;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }
    }
}

